import sys
import math
from queue import PriorityQueue
import bisect

# --- Data Models ---

class Server:
    def __init__(self, id, g, k, m):
        self.id = id
        self.g = g  # NPU count
        self.k = k  # Speed coefficient
        self.m = m  # Memory size

class NPU:
    def __init__(self, id, server_id, k, m):
        self.id = id
        self.server_id = server_id
        self.k = k
        self.m = m
        # List of (time, memory_delta) events
        self.events = []

    def add_task(self, start_time, finish_time, mem_needed):
        """Adds a task to the NPU's schedule."""
        bisect.insort(self.events, (start_time, mem_needed))
        bisect.insort(self.events, (finish_time, -mem_needed))

    def find_earliest_start_time(self, arrival_time, mem_needed):
        """Finds the earliest time a task can start on this NPU."""
        if mem_needed > self.m:
            return float('inf')

        # Find memory usage at arrival_time by summing events up to that point
        mem_at_arrival = sum(delta for t, delta in self.events if t <= arrival_time)

        if mem_at_arrival + mem_needed <= self.m:
            return arrival_time

        # If not enough memory, check future event points for released memory
        potential_start_time = float('inf')
        
        # Find the index of the first event after arrival_time
        event_idx = bisect.bisect_right(self.events, (arrival_time, float('inf')))

        # This temporary variable holds the memory usage as we iterate through future events
        mem_usage_tracker = mem_at_arrival

        # Iterate through events chronologically from arrival_time onwards
        for i in range(event_idx, len(self.events)):
            event_time, mem_delta = self.events[i]
            
            # Before processing the event at event_time, check if the task could fit
            # in the time slot just before it.
            # The condition `mem_usage_tracker + mem_needed <= self.m` should have been met
            # in a previous iteration if a slot was available.
            
            # Now, apply the current event's memory change
            mem_usage_tracker += mem_delta
            
            # After the memory change at event_time, check if the task can now fit.
            if mem_usage_tracker + mem_needed <= self.m:
                # A slot is available starting from event_time
                potential_start_time = event_time
                break
        
        return potential_start_time

class User:
    def __init__(self, id, s, e, cnt):
        self.id = id
        self.s = s
        self.e = e
        self.cnt = cnt
        self.samples_left = cnt
        self.next_send_time = s
        self.requests = []
        self.last_npu_id = -1

# --- Global Parameters ---

A, B = 0, 0
LATENCIES = []
MOVE_PENALTY = 25  # Heuristic penalty for switching NPUs, in ms

# --- Scheduler Logic ---

def solve():
    global A, B, LATENCIES

    # 1. Read Input
    try:
        line = sys.stdin.readline()
        if not line.strip(): return
        N = int(line)

        servers_data = [list(map(int, sys.stdin.readline().split())) for _ in range(N)]
        
        line = sys.stdin.readline()
        if not line.strip(): return
        M = int(line)

        users_data = [list(map(int, sys.stdin.readline().split())) for _ in range(M)]
        LATENCIES = [list(map(int, sys.stdin.readline().split())) for _ in range(N)]
        
        line = sys.stdin.readline()
        if not line.strip(): return
        A, B = map(int, line.split())

    except (IOError, ValueError) as e:
        # Handle empty input for local testing, and print error
        # print(f"Error reading input: {e}", file=sys.stderr)
        return

    # 2. Initialize Models
    servers = [Server(i, g, k, m) for i, (g, k, m) in enumerate(servers_data)]
    users = [User(i, s, e, cnt) for i, (s, e, cnt) in enumerate(users_data)]

    npus = []
    npu_counter = 0
    for server in servers:
        for _ in range(server.g):
            npus.append(NPU(npu_counter, server.id, server.k, server.m))
            npu_counter += 1

    # 3. Event-driven Scheduling
    user_pq = PriorityQueue()
    for user in users:
        user_pq.put((user.next_send_time, user.id))

    while not user_pq.empty():
        t, user_id = user_pq.get()
        user = users[user_id]

        if user.samples_left <= 0:
            continue

        send_time = max(t, user.next_send_time)
        
        # --- Decision Making ---
        best_option = {
            "cost": float('inf'),
            "finish_time": float('inf'),
            "npu": None,
            "batch_size": 0
        }

        for npu in npus:
            server = servers[npu.server_id]
            
            max_b_for_npu = (server.m - B) // A
            if max_b_for_npu <= 0:
                continue

            batch_size = min(user.samples_left, max_b_for_npu)
            mem_needed = A * batch_size + B
            
            latency = LATENCIES[server.id][user.id]
            arrival_time = send_time + latency
            
            start_time = npu.find_earliest_start_time(arrival_time, mem_needed)
            if start_time == float('inf'):
                continue

            inference_time = math.ceil(batch_size / server.k)
            finish_time = start_time + inference_time

            # Cost function to select the best NPU
            cost = finish_time
            if user.last_npu_id != -1 and npu.id != user.last_npu_id:
                cost += MOVE_PENALTY
            
            # If this option is better, update best_option
            if cost < best_option["cost"]:
                best_option = {
                    "cost": cost,
                    "finish_time": finish_time,
                    "npu": npu,
                    "batch_size": batch_size
                }

        # --- Commit to the best decision ---
        if best_option["npu"]:
            chosen_npu = best_option["npu"]
            batch = best_option["batch_size"]
            finish_time = best_option["finish_time"]
            
            mem_needed = A * batch + B
            latency = LATENCIES[chosen_npu.server_id][user.id]
            arrival_time = send_time + latency
            start_time = finish_time - math.ceil(batch / servers[chosen_npu.server_id].k)
            
            # Book the NPU
            chosen_npu.add_task(start_time, finish_time, mem_needed)
            
            # Record the decision for the user
            npu_id_in_server = 0
            base_npu_id = sum(servers[i].g for i in range(chosen_npu.server_id))
            npu_id_in_server = chosen_npu.id - base_npu_id + 1
            
            user.requests.append((send_time, chosen_npu.server_id + 1, npu_id_in_server, batch))
            
            # Update user state
            user.samples_left -= batch
            user.next_send_time = send_time + latency + 1
            user.last_npu_id = chosen_npu.id

            if user.samples_left > 0:
                user_pq.put((user.next_send_time, user.id))

    # 4. Print Output
    for user in users:
        # Sort requests by send time before printing, just in case.
        user.requests.sort()
        print(len(user.requests))
        flat_list = [item for tpl in user.requests for item in tpl]
        print(*flat_list)

if __name__ == "__main__":
    solve() 